import { getSession } from 'next-auth/react'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'

export class ApiClient {
  static async request(endpoint: string, options: RequestInit = {}) {
    const session = await getSession()

    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }

    // Add backend token if available
    if (session?.backendToken) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${session.backendToken}`
      }
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config)

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`)
    }

    return response.json()
  }

  static async get(endpoint: string) {
    return this.request(endpoint, { method: 'GET' })
  }

  static async post(endpoint: string, data: unknown) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  static async put(endpoint: string, data: unknown) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  static async delete(endpoint: string) {
    return this.request(endpoint, { method: 'DELETE' })
  }
}
