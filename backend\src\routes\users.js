const express = require('express')
const { body, param, query, validationResult } = require('express-validator')
const { authenticate } = require('../middleware/auth')
const { checkPermission } = require('../middleware/authorization')
const { hashPassword } = require('../utils/auth')
const prisma = require('../utils/database')
const logger = require('../utils/logger')

const router = express.Router()

// Apply authentication to all routes
router.use(authenticate)

// Validation rules
const createUserValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('username')
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, and underscores'),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must be at least 8 characters with uppercase, lowercase, number, and special character'),
  body('firstName').optional().isLength({ min: 1, max: 50 }).trim().withMessage('First name must be 1-50 characters'),
  body('lastName').optional().isLength({ min: 1, max: 50 }).trim().withMessage('Last name must be 1-50 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

const updateUserValidation = [
  param('id').isString().withMessage('User ID must be a string'),
  body('email').optional().isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('username')
    .optional()
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, and underscores'),
  body('firstName').optional().isLength({ min: 1, max: 50 }).trim().withMessage('First name must be 1-50 characters'),
  body('lastName').optional().isLength({ min: 1, max: 50 }).trim().withMessage('Last name must be 1-50 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

const changePasswordValidation = [
  param('id').isString().withMessage('User ID must be a string'),
  body('newPassword')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must be at least 8 characters with uppercase, lowercase, number, and special character')
]

/**
 * @route   GET /api/users
 * @desc    Get all users with pagination and filtering
 * @access  Private (requires Users:read permission)
 */
router.get('/', checkPermission('Users', 'read'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const search = req.query.search || ''
    const isActive = req.query.isActive

    const skip = (page - 1) * limit

    // Build where clause
    const where = {}

    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { username: { contains: search, mode: 'insensitive' } },
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (isActive !== undefined) {
      where.isActive = isActive === 'true'
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          groupMemberships: {
            include: {
              group: {
                select: {
                  id: true,
                  name: true,
                  description: true
                }
              }
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ])

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    logger.error('Get users error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get users'
    })
  }
})

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID
 * @access  Private (requires Users:read permission)
 */
router.get('/:id', checkPermission('Users', 'read'), async (req, res) => {
  try {
    const { id } = req.params

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        groupMemberships: {
          include: {
            group: {
              select: {
                id: true,
                name: true,
                description: true
              }
            }
          }
        }
      }
    })

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      })
    }

    res.json({
      success: true,
      data: { user }
    })
  } catch (error) {
    logger.error('Get user error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get user'
    })
  }
})

/**
 * @route   POST /api/users
 * @desc    Create new user
 * @access  Private (requires Users:create permission)
 */
router.post('/', checkPermission('Users', 'create'), createUserValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { email, username, password, firstName, lastName, isActive = true } = req.body

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ email }, { username }]
      }
    })

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email or username already exists'
      })
    }

    // Hash password
    const hashedPassword = await hashPassword(password)

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        firstName: firstName || null,
        lastName: lastName || null,
        isActive
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isActive: true,
        createdAt: true
      }
    })

    logger.info(`User created by ${req.user.email}: ${user.email}`)

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: { user }
    })
  } catch (error) {
    logger.error('Create user error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to create user'
    })
  }
})

/**
 * @route   PUT /api/users/:id
 * @desc    Update user
 * @access  Private (requires Users:update permission)
 */
router.put('/:id', checkPermission('Users', 'update'), updateUserValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { id } = req.params
    const { email, username, firstName, lastName, isActive } = req.body

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      })
    }

    // Check for email/username conflicts
    if (email || username) {
      const conflictUser = await prisma.user.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [...(email ? [{ email }] : []), ...(username ? [{ username }] : [])]
            }
          ]
        }
      })

      if (conflictUser) {
        return res.status(400).json({
          success: false,
          error: 'User with this email or username already exists'
        })
      }
    }

    // Update user
    const updateData = {}
    if (email !== undefined) updateData.email = email
    if (username !== undefined) updateData.username = username
    if (firstName !== undefined) updateData.firstName = firstName
    if (lastName !== undefined) updateData.lastName = lastName
    if (isActive !== undefined) updateData.isActive = isActive

    const user = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isActive: true,
        updatedAt: true
      }
    })

    logger.info(`User updated by ${req.user.email}: ${user.email}`)

    res.json({
      success: true,
      message: 'User updated successfully',
      data: { user }
    })
  } catch (error) {
    logger.error('Update user error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to update user'
    })
  }
})

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete user
 * @access  Private (requires Users:delete permission)
 */
router.delete('/:id', checkPermission('Users', 'delete'), async (req, res) => {
  try {
    const { id } = req.params

    // Prevent self-deletion
    if (id === req.user.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete your own account'
      })
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      })
    }

    // Delete user (cascade will handle related records)
    await prisma.user.delete({
      where: { id }
    })

    logger.info(`User deleted by ${req.user.email}: ${existingUser.email}`)

    res.json({
      success: true,
      message: 'User deleted successfully'
    })
  } catch (error) {
    logger.error('Delete user error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to delete user'
    })
  }
})

/**
 * @route   PUT /api/users/:id/password
 * @desc    Change user password
 * @access  Private (requires Users:update permission)
 */
router.put('/:id/password', checkPermission('Users', 'update'), changePasswordValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { id } = req.params
    const { newPassword } = req.body

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      })
    }

    // Hash new password
    const hashedPassword = await hashPassword(newPassword)

    // Update password
    await prisma.user.update({
      where: { id },
      data: { password: hashedPassword }
    })

    logger.info(`Password changed by ${req.user.email} for user: ${existingUser.email}`)

    res.json({
      success: true,
      message: 'Password changed successfully'
    })
  } catch (error) {
    logger.error('Change password error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to change password'
    })
  }
})

/**
 * @route   POST /api/users/by-email
 * @desc    Get user by email
 * @access  Public
 */
router.post('/by-email', async (req, res) => {
  try {
    const { email } = req.body

    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    })

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      })
    }

    res.json({
      success: true,
      data: { user }
    })
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Failed to get user'
    })
  }
})

module.exports = router
